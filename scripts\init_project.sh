#!/bin/bash
# STM32 Ninja - 项目初始化脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 输出函数
info() { echo -e "${BLUE}[信息]${NC} $1"; }
success() { echo -e "${GREEN}[成功]${NC} $1"; }
warn() { echo -e "${YELLOW}[警告]${NC} $1"; }
error() { echo -e "${RED}[错误]${NC} $1"; }

# 检测项目类型
detect_project_type() {
    local dir="${1:-.}"
    
    # 检测Keil工程 - 改进的检测逻辑
    if find "$dir" -maxdepth 2 -name "*.uvprojx" -type f 2>/dev/null | grep -q .; then
        echo "keil"
        return
    fi
    
    # 检测CMake工程
    if [ -f "$dir/CMakeLists.txt" ]; then
        echo "cmake"
        return
    fi
    
    # 检测Makefile工程
    if [ -f "$dir/Makefile" ]; then
        echo "makefile"
        return
    fi
    
    echo "unknown"
}

# 在项目目录中初始化STM32 Ninja支持
init_project() {
    local project_dir="${1:-.}"
    
    # 转到项目目录
    cd "$project_dir" || {
        error "无法进入目录: $project_dir"
        exit 1
    }
    
    info "检测项目类型..."
    local project_type=$(detect_project_type ".")
    
    case "$project_type" in
        keil)
            success "检测到Keil工程"
            local uvprojx_file=$(find . -maxdepth 2 -name "*.uvprojx" -type f | head -1)
            info "工程文件: $uvprojx_file"
            ;;
        cmake)
            success "检测到CMake工程"
            ;;
        makefile)
            success "检测到Makefile工程"
            ;;
        unknown)
            warn "未检测到已知的工程类型"
            echo "支持的工程类型："
            echo "  - Keil (*.uvprojx)"
            echo "  - CMake (CMakeLists.txt)"
            echo "  - Makefile"
            ;;
    esac
    
    # 检查是否已初始化
    if [ -f ".stm32ninja" ]; then
        warn "项目已初始化"
        echo -n "是否重新初始化? (y/N): "
        read -n 1 confirm
        echo ""
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            return 0
        fi
    fi
    
    # 创建项目配置
    cat > ".stm32ninja" << EOL
# STM32 Ninja 项目配置
PROJECT_TYPE=$project_type
NINJA_VERSION=2.0
INIT_DATE=$(date +%Y-%m-%d)
PROJECT_DIR=$(pwd)
EOL
    
    # 创建ninja.sh脚本（不是符号链接，因为Windows下符号链接有问题）
    cat > "ninja.sh" << 'NINJA_SH'
#!/bin/bash
# STM32 Ninja - 项目启动脚本

# 获取项目目录
PROJECT_DIR="$(dirname "$(readlink -f "$0" 2>/dev/null || realpath "$0" 2>/dev/null || echo "$0")")"

# 查找全局安装
if [ -z "$STM32_NINJA_HOME" ]; then
    # 尝试常见位置
    for path in "/d/STM32Ninja" "$HOME/.stm32ninja" "/opt/stm32ninja"; do
        if [ -f "$path/scripts/ninja_main.sh" ]; then
            export STM32_NINJA_HOME="$path"
            break
        fi
    done
fi

if [ -z "$STM32_NINJA_HOME" ] || [ ! -f "$STM32_NINJA_HOME/scripts/ninja_main.sh" ]; then
    echo "[错误] 未找到STM32 Ninja全局安装"
    echo "请先运行全局安装脚本"
    exit 1
fi

# 设置项目环境
export NINJA_PROJECT_DIR="$PROJECT_DIR"
cd "$PROJECT_DIR"

# 运行主程序
exec "$STM32_NINJA_HOME/scripts/ninja_main.sh" "$@"
NINJA_SH
    chmod +x "ninja.sh"
    
    success "项目初始化完成！"
    echo ""
    echo "项目信息："
    echo "  类型: $project_type"
    echo "  目录: $(pwd)"
    echo ""
    echo "使用方法："
    echo "  ./ninja.sh          # 启动交互式菜单"
    echo "  ./ninja.sh build    # 直接编译"
    echo "  ./ninja.sh flash    # 直接烧录"
    echo "  ./ninja.sh list     # 列出编译文件"
    echo "  ./ninja.sh add      # 添加文件到编译"
    
    # 创建配置目录
    mkdir -p config
    
    # 如果是Keil项目，询问是否生成build.ninja
    if [ "$project_type" = "keil" ]; then
        echo ""
        echo -n "是否立即生成Ninja编译文件? (Y/n): "
        read -n 1 gen_ninja
        echo ""
        if [ "$gen_ninja" != "n" ] && [ "$gen_ninja" != "N" ]; then
            if [ -f "$STM32_NINJA_HOME/scripts/keil_to_ninja_smart.sh" ]; then
                "$STM32_NINJA_HOME/scripts/keil_to_ninja_smart.sh"
            elif [ -f "$STM32_NINJA_HOME/scripts/keil_to_ninja.sh" ]; then
                "$STM32_NINJA_HOME/scripts/keil_to_ninja.sh"
            else
                warn "未找到Keil转换脚本，请手动运行 './ninja.sh build'"
            fi
        fi
    fi
}

# 主函数
case "$1" in
    init)
        init_project "$2"
        ;;
    version|--version|-v)
        echo "STM32 Ninja v2.0"
        ;;
    help|--help|-h)
        echo "STM32 Ninja - 全局命令"
        echo ""
        echo "用法:"
        echo "  stm32ninja init [目录]   - 在指定目录初始化项目"
        echo "  stm32ninja version       - 显示版本信息"
        echo "  stm32ninja help          - 显示帮助信息"
        echo ""
        echo "示例:"
        echo "  cd /path/to/project"
        echo "  stm32ninja init"
        ;;
    *)
        echo "用法: stm32ninja <命令> [参数]"
        echo "运行 'stm32ninja help' 查看帮助"
        ;;
esac
