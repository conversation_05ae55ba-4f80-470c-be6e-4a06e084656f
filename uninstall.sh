#!/bin/bash
# STM32 Ninja 卸载脚本

echo "卸载 STM32 Ninja..."
echo "安装目录: /d/STM32Ninja"
echo ""
echo -n "确定要卸载吗? (y/N): "
read -n 1 confirm
echo ""

if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
    # 删除安装目录
    rm -rf "/d/STM32Ninja"
    
    # 提示清理环境变量
    echo ""
    echo "请手动从以下文件中删除STM32_NINJA相关的环境变量："
    echo "  - ~/.bashrc"
    echo "  - ~/.bash_profile"
    echo ""
    echo "卸载完成"
else
    echo "取消卸载"
fi
