#!/bin/bash
# CMSIS-DAP 调试脚本

echo "CMSIS-DAP 调试工具"
echo "=================="
echo ""

# 设置调试模式
export DEBUG=1

# 运行检测脚本
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
"$SCRIPT_DIR/detect_programmer.sh" --debug

echo ""
echo "调试信息收集完成"
echo ""
echo "如果看到错误信息，请检查："
echo "1. USB驱动是否正确安装（推荐使用Zadig安装WinUSB驱动）"
echo "2. 设备管理器中是否能看到CMSIS-DAP设备"
echo "3. 是否有其他程序占用了该设备"
echo ""
echo "按任意键退出..."
read -n 1