#!/bin/bash
# 生成HEX文件的辅助脚本

if [ -f "build/ElectronicController_Board.elf" ]; then
    echo "生成HEX文件..."
    arm-none-eabi-objcopy -O ihex build/ElectronicController_Board.elf build/ElectronicController_Board.hex
    if [ $? -eq 0 ]; then
        echo "HEX文件生成成功: build/ElectronicController_Board.hex"
        arm-none-eabi-size build/ElectronicController_Board.elf
    else
        echo "HEX文件生成失败"
        exit 1
    fi
else
    echo "错误: 未找到ELF文件"
    exit 1
fi