#!/bin/bash
# STM32 Ninja - 检测烧录器脚本

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || exit 1

# 主程序
main() {
    info "检测烧录器..."
    
    # 检查是否启用调试模式
    local debug=false
    if [[ "$1" == "--debug" ]] || [[ "$DEBUG" == "1" ]]; then
        debug=true
        echo "调试模式已启用"
    fi
    
    local openocd=$(find_openocd)
    if [ -z "$openocd" ]; then
        error "未找到OpenOCD工具"
        exit 1
    fi
    
    if $debug; then
        echo "OpenOCD路径: $openocd"
        echo ""
    fi
    
    # 检测STLink
    step "检查ST-Link..."
    if $openocd -f interface/stlink.cfg -c "init; exit" &>/dev/null; then
        success "检测到ST-Link"
        echo ""
        # 获取更多信息
        $openocd -f interface/stlink.cfg -c "init" -c "exit" 2>&1 | grep -E "(voltage|serial|version)" | while read line; do
            echo "  $line"
        done
        exit 0
    fi
    
    # 检测CMSIS-DAP
    step "检查CMSIS-DAP..."
    
    # 首先尝试标准的cmsis-dap.cfg并强制SWD模式
    if $debug; then
        echo "尝试CMSIS-DAP with SWD..."
        $openocd -f interface/cmsis-dap.cfg -c "transport select swd" -c "adapter speed 1000" -c "init; exit" 2>&1 | tail -10
    fi
    
    if $openocd -f interface/cmsis-dap.cfg -c "transport select swd" -c "adapter speed 1000" -c "init; exit" &>/dev/null 2>&1; then
        success "检测到CMSIS-DAP (SWD模式)"
        echo ""
        # 获取设备信息
        $openocd -f interface/cmsis-dap.cfg -c "transport select swd" -c "adapter speed 1000" -c "init" -c "exit" 2>&1 | grep -E "(CMSIS-DAP|FW Version|Serial)" | while read line; do
            echo "  $line"
        done
        exit 0
    fi
    
    # 尝试其他配置
    local dap_configs=(
        "$SCRIPT_DIR/../config/dap-swd.cfg"
        "interface/cmsis-dap.cfg"
    )
    
    for config in "${dap_configs[@]}"; do
        if $debug; then
            echo "尝试配置: $config"
            $openocd -f "$config" -c "adapter speed 1000" -c "init; exit" 2>&1 | tail -5
        fi
        
        if $openocd -f "$config" -c "adapter speed 1000" -c "init; exit" &>/dev/null 2>&1; then
            success "检测到CMSIS-DAP"
            echo ""
            # 获取更多信息
            $openocd -f "$config" -c "adapter speed 1000" -c "init" -c "exit" 2>&1 | grep -E "(CMSIS-DAP|found|voltage|serial)" | while read line; do
                echo "  $line"
            done
            exit 0
        fi
    done
    
    # 如果还是检测不到，尝试使用通用HID配置
    if $debug; then
        echo "尝试通用CMSIS-DAP配置..."
        $openocd -c "adapter driver cmsis-dap" -c "adapter speed 1000" -c "init; exit" 2>&1 | tail -5
    fi
    
    if $openocd -c "adapter driver cmsis-dap" -c "adapter speed 1000" -c "init; exit" &>/dev/null 2>&1; then
        success "检测到CMSIS-DAP (HID模式)"
        exit 0
    fi
    
    # 检测J-Link
    step "检查J-Link..."
    if $openocd -f interface/jlink.cfg -c "init; exit" &>/dev/null; then
        success "检测到J-Link"
        exit 0
    fi
    
    # 未检测到烧录器
    warn "未检测到支持的烧录器"
    echo ""
    echo "支持的烧录器："
    echo "  • ST-Link V2/V3"
    echo "  • CMSIS-DAP"
    echo "  • J-Link"
    echo ""
    echo "请检查："
    echo "  1. 烧录器是否正确连接到USB"
    echo "  2. 驱动程序是否已安装"
    echo "  3. 烧录器是否被其他程序占用"
    
    exit 1
}

# 执行主程序
main "$@"