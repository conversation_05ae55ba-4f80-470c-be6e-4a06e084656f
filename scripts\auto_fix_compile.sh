#!/bin/bash
# STM32 Ninja - 自动修复编译错误

# 加载通用函数
source "$(dirname "$0")/common.sh"

# 分析编译错误日志
analyze_error() {
    local error_log="${1:-build_error.log}"
    
    if [ ! -f "$error_log" ]; then
        error "未找到错误日志文件: $error_log"
        return 1
    fi
    
    info "分析编译错误..."
    
    # 检测错误类型
    local has_undefined=false
    local has_startup_error=false
    local has_missing_file=false
    
    if grep -q "undeclared\|undefined reference" "$error_log"; then
        has_undefined=true
    fi
    
    if grep -q "bad instruction\|Error: junk at end of line" "$error_log" && grep -q "startup" "$error_log"; then
        has_startup_error=true
    fi
    
    if grep -q "No such file or directory" "$error_log"; then
        has_missing_file=true
    fi
    
    # 返回错误类型
    if [ "$has_undefined" = true ]; then
        echo "undefined"
    elif [ "$has_startup_error" = true ]; then
        echo "startup"
    elif [ "$has_missing_file" = true ]; then
        echo "missing_file"
    else
        echo "unknown"
    fi
}

# 修复未定义错误
fix_undefined_errors() {
    local error_log="${1:-build_error.log}"
    
    info "修复未定义的符号错误..."
    
    # 提取所有未定义的符号
    local undefined_symbols=$(grep -o "'[A-Za-z_][A-Za-z0-9_]*' undeclared" "$error_log" | sed "s/'//g" | sed "s/ undeclared//g" | sort -u)
    
    if [ -z "$undefined_symbols" ]; then
        # 尝试其他格式
        undefined_symbols=$(grep -o "undefined reference to .[A-Za-z_][A-Za-z0-9_]*" "$error_log" | sed "s/undefined reference to .//g" | sed "s/'//g" | sort -u)
    fi
    
    if [ -n "$undefined_symbols" ]; then
        echo "发现未定义的符号："
        echo "$undefined_symbols"
        echo ""
        
        # 搜索并修复每个符号
        local fixed=false
        for symbol in $undefined_symbols; do
            step "搜索符号: $symbol"
            
            # 查找包含该符号的头文件
            local found_files=$(find . .. -name "*.h" -type f 2>/dev/null | xargs grep -l "\b$symbol\b" 2>/dev/null | head -10)
            
            if [ -n "$found_files" ]; then
                echo "找到定义在以下文件："
                local inc_paths=""
                for file in $found_files; do
                    echo "  - $file"
                    local dir=$(dirname "$file")
                    inc_paths="$inc_paths -I$dir"
                done
                
                # 更新build.ninja
                if [ -f "build.ninja" ] && [ -n "$inc_paths" ]; then
                    update_build_ninja "$inc_paths"
                    fixed=true
                fi
            fi
        done
        
        if [ "$fixed" = true ]; then
            success "已添加缺失的包含路径"
            return 0
        fi
    fi
    
    return 1
}

# 修复启动文件错误
fix_startup_errors() {
    info "修复启动文件错误..."
    
    # 查找Keil格式的启动文件
    local startup_files=$(find . -name "startup*.s" -o -name "startup*.S" 2>/dev/null)
    
    for file in $startup_files; do
        if grep -q "AREA\|EXPORT\|DCD\|ENDP" "$file" 2>/dev/null; then
            warn "发现Keil格式启动文件: $file"
            
            # 转换文件
            local gnu_file="${file%.s}_gnu.s"
            if ./scripts/convert_keil_startup.sh "$file" "$gnu_file"; then
                # 更新build.ninja
                if [ -f "build.ninja" ]; then
                    sed -i "s|$file|$gnu_file|g" "build.ninja"
                    success "已转换并更新启动文件"
                    return 0
                fi
            fi
        fi
    done
    
    return 1
}

# 修复缺失文件错误
fix_missing_file_errors() {
    local error_log="${1:-build_error.log}"
    
    info "修复缺失文件错误..."
    
    # 提取缺失的文件
    local missing_files=$(grep -o "[^:]*: No such file or directory" "$error_log" | sed 's/: No such file or directory//g' | sort -u)
    
    if [ -n "$missing_files" ]; then
        echo "缺失的文件："
        echo "$missing_files"
        echo ""
        
        # 尝试查找文件
        for file in $missing_files; do
            local basename=$(basename "$file")
            step "搜索文件: $basename"
            
            local found=$(find . .. ../.. -name "$basename" -type f 2>/dev/null | head -1)
            if [ -n "$found" ]; then
                echo "  找到: $found"
                local dir=$(dirname "$found")
                update_build_ninja "-I$dir"
            fi
        done
    fi
}

# 更新build.ninja
update_build_ninja() {
    local new_paths="$1"
    
    if [ -f "build.ninja" ] && [ -n "$new_paths" ]; then
        # 备份
        [ ! -f "build.ninja.bak" ] && cp build.ninja build.ninja.bak
        
        # 获取当前includes
        local current=$(grep "^includes =" build.ninja | sed 's/^includes = //')
        
        # 合并并去重
        local merged="$current $new_paths"
        merged=$(echo "$merged" | tr ' ' '\n' | sort -u | grep -v "^$" | tr '\n' ' ')
        
        # 更新
        sed -i "s|^includes =.*|includes = $merged|" build.ninja
        
        info "已更新包含路径"
    fi
}

# 主函数
main() {
    step "STM32 Ninja 自动修复工具"
    echo ""
    
    # 分析错误类型
    local error_type=$(analyze_error "$1")
    
    case "$error_type" in
        undefined)
            info "检测到未定义符号错误"
            if fix_undefined_errors "$1"; then
                success "修复完成，请重新编译"
                exit 0
            fi
            ;;
        startup)
            info "检测到启动文件格式错误"
            if fix_startup_errors; then
                success "修复完成，请重新编译"
                exit 0
            fi
            ;;
        missing_file)
            info "检测到文件缺失错误"
            if fix_missing_file_errors "$1"; then
                success "修复完成，请重新编译"
                exit 0
            fi
            ;;
        unknown)
            warn "未能识别错误类型"
            ;;
    esac
    
    # 如果特定修复失败，运行通用修复
    warn "尝试通用修复..."
    
    # 添加所有可能的包含路径
    local all_inc=""
    for dir in $(find . .. -type d \( -name "Inc" -o -name "inc" -o -name "Include" -o -name "Components" \) 2>/dev/null | head -20); do
        all_inc="$all_inc -I$dir"
    done
    
    if [ -n "$all_inc" ]; then
        update_build_ninja "$all_inc"
        success "已添加额外的包含路径，请重新编译"
    else
        error "自动修复失败，请手动检查错误"
        exit 1
    fi
}

# 运行主函数
main "$@"