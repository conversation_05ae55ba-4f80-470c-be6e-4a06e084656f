#!/bin/bash
# STM32 Ninja 主命令

NINJA_HOME="$(dirname "$(dirname "$(readlink -f "$0")")")"
export STM32_NINJA_HOME="$NINJA_HOME"

# 根据命令执行不同的脚本
case "$1" in
    init|version|--version|-v|help|--help|-h)
        exec "$NINJA_HOME/scripts/init_project.sh" "$@"
        ;;
    *)
        # 其他命令交给用户提示
        echo "用法: stm32ninja <命令> [参数]"
        echo "运行 'stm32ninja help' 查看帮助"
        exit 1
        ;;
esac
