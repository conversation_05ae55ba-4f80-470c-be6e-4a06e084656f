#!/bin/bash
# STM32 Ninja - 添加文件到编译工程

# 加载通用函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh" || {
    echo "[错误] 无法加载common.sh"
    exit 1
}

# 显示帮助
show_help() {
    cat << EOF
用法: $0 [选项] <文件或目录>

选项:
    -t, --type <type>     文件类型 (c, cpp, s, h)
    -g, --group <name>    分组名称 (默认: User)
    -r, --recursive       递归添加目录中的所有文件
    -p, --pattern <pat>   文件模式 (如 *.c, *.h)
    -h, --help           显示此帮助信息

示例:
    $0 my_lib.c                     # 添加单个文件
    $0 -r libs/my_library           # 递归添加目录
    $0 -t c -p "*.c" drivers/       # 添加所有.c文件
    $0 -g "MyLibrary" lib/*.c       # 添加到指定分组
EOF
}

# 解析命令行参数
parse_args() {
    local type=""
    local group="User"
    local recursive=false
    local pattern=""
    local files=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                type="$2"
                shift 2
                ;;
            -g|--group)
                group="$2"
                shift 2
                ;;
            -r|--recursive)
                recursive=true
                shift
                ;;
            -p|--pattern)
                pattern="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                files+=("$1")
                shift
                ;;
        esac
    done
    
    # 验证参数
    if [ ${#files[@]} -eq 0 ]; then
        error "请指定要添加的文件或目录"
        show_help
        exit 1
    fi
    
    # 添加文件
    for item in "${files[@]}"; do
        add_to_build "$item" "$type" "$group" "$recursive" "$pattern"
    done
}

# 获取文件类型
get_file_type() {
    local file="$1"
    local ext="${file##*.}"
    
    case "$ext" in
        c) echo "c" ;;
        cpp|cc|cxx) echo "cpp" ;;
        s|S|asm) echo "asm" ;;
        h|hpp) echo "header" ;;
        *) echo "unknown" ;;
    esac
}

# 添加文件到build.ninja
add_to_build() {
    local path="$1"
    local type="$2"
    local group="$3"
    local recursive="$4"
    local pattern="$5"
    
    # 检查build.ninja是否存在
    if [ ! -f "build.ninja" ]; then
        error "未找到build.ninja文件，请先初始化项目"
        exit 1
    fi
    
    # 备份build.ninja
    cp build.ninja build.ninja.bak
    
    # 收集要添加的文件
    local files_to_add=()
    
    if [ -f "$path" ]; then
        # 单个文件
        files_to_add+=("$path")
    elif [ -d "$path" ]; then
        # 目录
        if [ "$recursive" = true ]; then
            # 递归查找
            if [ -n "$pattern" ]; then
                while IFS= read -r -d '' file; do
                    files_to_add+=("$file")
                done < <(find "$path" -type f -name "$pattern" -print0)
            else
                while IFS= read -r -d '' file; do
                    local ftype=$(get_file_type "$file")
                    if [[ "$ftype" != "unknown" && "$ftype" != "header" ]]; then
                        files_to_add+=("$file")
                    fi
                done < <(find "$path" -type f -print0)
            fi
        else
            # 非递归
            if [ -n "$pattern" ]; then
                for file in "$path"/$pattern; do
                    [ -f "$file" ] && files_to_add+=("$file")
                done
            else
                for file in "$path"/*; do
                    if [ -f "$file" ]; then
                        local ftype=$(get_file_type "$file")
                        if [[ "$ftype" != "unknown" && "$ftype" != "header" ]]; then
                            files_to_add+=("$file")
                        fi
                    fi
                done
            fi
        fi
    else
        error "路径不存在: $path"
        return 1
    fi
    
    if [ ${#files_to_add[@]} -eq 0 ]; then
        warn "没有找到要添加的文件"
        return 0
    fi
    
    info "找到 ${#files_to_add[@]} 个文件要添加"
    
    # 创建临时文件
    local temp_file=$(mktemp)
    local in_source_section=false
    local added_files=()
    
    # 读取现有的build.ninja
    while IFS= read -r line; do
        echo "$line" >> "$temp_file"
        
        # 检测源文件部分
        if [[ "$line" == "# Source files" ]]; then
            in_source_section=true
        elif [[ "$line" == "# Link" ]]; then
            if [ "$in_source_section" = true ]; then
                # 在链接部分之前添加新文件
                echo "" >> "$temp_file"
                echo "# Added by add_files.sh - Group: $group" >> "$temp_file"
                
                for file in "${files_to_add[@]}"; do
                    local ftype=$(get_file_type "$file")
                    local obj_name="${file//\//_}"
                    obj_name="${obj_name//../_}"
                    obj_name="${obj_name%.${file##*.}}.o"
                    
                    case "$ftype" in
                        c|cpp)
                            echo "build \$builddir/${group}_${obj_name}: cc $file" >> "$temp_file"
                            added_files+=("\$builddir/${group}_${obj_name}")
                            info "添加: $file"
                            ;;
                        asm)
                            echo "build \$builddir/${group}_${obj_name}: as $file" >> "$temp_file"
                            added_files+=("\$builddir/${group}_${obj_name}")
                            info "添加: $file"
                            ;;
                    esac
                done
                echo "" >> "$temp_file"
            fi
            in_source_section=false
        fi
    done < build.ninja
    
    # 更新链接行
    if [ ${#added_files[@]} -gt 0 ]; then
        # 创建最终的build.ninja
        local final_file=$(mktemp)
        
        while IFS= read -r line; do
            if [[ "$line" == build\ \$builddir/\$target.elf:\ link\ * ]]; then
                # 在链接行添加新的目标文件
                local link_line="$line"
                for obj in "${added_files[@]}"; do
                    link_line="$link_line $obj"
                done
                echo "$link_line" >> "$final_file"
            else
                echo "$line" >> "$final_file"
            fi
        done < "$temp_file"
        
        # 替换原文件
        mv "$final_file" build.ninja
        success "成功添加 ${#added_files[@]} 个文件到编译工程"
        
        # 更新包含路径（如果需要）
        update_include_paths "${files_to_add[@]}"
    else
        # 恢复备份
        mv build.ninja.bak build.ninja
        warn "没有文件被添加"
    fi
    
    # 清理临时文件
    rm -f "$temp_file"
}

# 更新包含路径
update_include_paths() {
    local files=("$@")
    local new_paths=()
    
    # 收集所有唯一的目录路径
    for file in "${files[@]}"; do
        local dir=$(dirname "$file")
        if [[ ! " ${new_paths[@]} " =~ " ${dir} " ]]; then
            new_paths+=("$dir")
        fi
    done
    
    if [ ${#new_paths[@]} -eq 0 ]; then
        return
    fi
    
    info "检查是否需要添加包含路径..."
    
    # 读取当前的包含路径
    local current_includes=$(grep "^includes =" build.ninja | sed 's/^includes = //')
    
    # 检查哪些路径需要添加
    local paths_to_add=()
    for path in "${new_paths[@]}"; do
        if [[ ! "$current_includes" =~ "-I$path" ]]; then
            paths_to_add+=("-I$path")
        fi
    done
    
    if [ ${#paths_to_add[@]} -gt 0 ]; then
        # 更新includes行
        local new_includes="$current_includes ${paths_to_add[*]}"
        sed -i "s|^includes =.*|includes = $new_includes|" build.ninja
        info "添加了 ${#paths_to_add[@]} 个包含路径"
    fi
}

# 列出当前工程中的文件
list_files() {
    if [ ! -f "build.ninja" ]; then
        error "未找到build.ninja文件"
        exit 1
    fi
    
    info "当前工程中的源文件："
    echo ""
    
    # 提取所有build规则中的源文件
    # 创建临时文件存储结果
    local temp_file=$(mktemp)
    
    # 使用grep查找所有编译规则
    grep -E "^build .+: (cc|as) " build.ninja > "$temp_file" 2>/dev/null
    
    # 逐行处理
    while IFS= read -r line; do
        # 提取源文件路径（最后一个字段）
        local src=$(echo "$line" | awk '{print $NF}')
        # 提取编译类型
        local type=$(echo "$line" | awk -F': ' '{print $2}' | awk '{print $1}')
        
        if [ -n "$src" ] && [ -n "$type" ]; then
            printf "%-60s [%s]\n" "$src" "$type"
        fi
    done < "$temp_file" | sort
    
    # 清理临时文件
    rm -f "$temp_file"
    
    echo ""
    local total=$(grep -c -E "^build .+: (cc|as) " build.ninja)
    info "总计: $total 个文件"
}

# 主函数
main() {
    # 如果没有参数，显示当前文件列表
    if [ $# -eq 0 ]; then
        list_files
        exit 0
    fi
    
    # 解析并执行命令
    parse_args "$@"
}

# 执行主函数
main "$@"